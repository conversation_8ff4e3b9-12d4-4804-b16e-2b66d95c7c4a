package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.aspose.words.FontSettings;
import com.aspose.words.License;
import com.aspose.words.PdfFontEmbeddingMode;
import com.aspose.words.PdfSaveOptions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.TableRenderData;
import com.deepoove.poi.data.Tables;
import com.deepoove.poi.data.style.ParagraphStyle;
import com.deepoove.poi.data.style.RowStyle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.mapper.BusiBidderInfoMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.kaifangqian.controller.vo.request.PositionRequest;
import com.ruoyi.kaifangqian.controller.vo.request.SignRequest;
import com.ruoyi.kaifangqian.enums.SignTypeEnum;
import com.ruoyi.kaifangqian.sdkservice.enums.APIResultEnum;
import com.ruoyi.kaifangqian.sdkservice.enums.SDKSignTypeEnum;
import com.ruoyi.kaifangqian.sdkservice.service.CalculatePositionService;
import com.ruoyi.kaifangqian.sdkservice.service.SDKService;
import com.ruoyi.kaifangqian.sdkservice.service.pojo.RealPositionProperty;
import com.ruoyi.kaifangqian.sdkservice.utils.Base64;
import com.ruoyi.kaifangqian.sdkservice.vo.base.Result;
import com.ruoyi.kaifangqian.sdkservice.vo.base.SignLocation;
import com.ruoyi.kaifangqian.sdkservice.vo.request.CertEventRequest;
import com.ruoyi.kaifangqian.sdkservice.vo.request.DocumentSignRequest;
import com.ruoyi.kaifangqian.sdkservice.vo.response.CertEventResponse;
import com.ruoyi.kaifangqian.sdkservice.vo.response.DocumentSignResponse;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.service.IDocResponseEntInfoService;
import com.ruoyi.utils.AttachmentUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.ruoyi.kaifangqian.sdkservice.utils.Base64.decode;
import static com.ruoyi.kaifangqian.sdkservice.utils.Base64.encode;

/**
 * 参与投标人信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
public class BusiBidderInfoServiceImpl extends ServiceImpl<BusiBidderInfoMapper, BusiBidderInfo> implements IBusiBidderInfoService {
    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;
    @Resource
    IBusiBidderInfoService busiBidderInfoService;
    @Resource
    IBusiBiddingRecordService busiBiddingRecordService;
    @Resource
    IDocResponseEntInfoService docResponseEntInfoService;
    @Resource
    IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private CalculatePositionService calculatePositionService;
    @Resource
    IBaseEntInfoService iBaseEntInfoService;
    @Autowired
    private SDKService sdkService;
    @Autowired
    private IBusiBidOpeningService iBusiBidOpeningService;
    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 查询参与投标人信息列表
     *
     * @param busiBidderInfo 参与投标人信息
     * @return 参与投标人信息
     */
    @Override
    public List<BusiBidderInfo> selectList(BusiBidderInfo busiBidderInfo) {
        QueryWrapper<BusiBidderInfo> busiBidderInfoQueryWrapper = new QueryWrapper<>();
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getProjectId()), "project_id", busiBidderInfo.getProjectId());
        busiBidderInfoQueryWrapper.like(ObjectUtil.isNotEmpty(busiBidderInfo.getBidderName()), "bidder_name", busiBidderInfo.getBidderName());
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getBidderCode()), "bidder_code", busiBidderInfo.getBidderCode());
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getDecodeFlag()), "decode_flag", busiBidderInfo.getDecodeFlag());
        String beginDecodeTime = busiBidderInfo.getParams().get("beginDecodeTime") != null ? busiBidderInfo.getParams().get("beginDecodeTime") + "" : "";
        String endDecodeTime = busiBidderInfo.getParams().get("endDecodeTime") + "" != null ? busiBidderInfo.getParams().get("endDecodeTime") + "" : "";
        busiBidderInfoQueryWrapper.between(ObjectUtil.isNotEmpty(beginDecodeTime) && ObjectUtil.isNotEmpty(endDecodeTime), "decode_time", beginDecodeTime, endDecodeTime);
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getBidderAmount()), "bidder_amount", busiBidderInfo.getBidderAmount());
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getRanking()), "ranking", busiBidderInfo.getRanking());
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getScore()), "score", busiBidderInfo.getScore());
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getIsWin()), "is_win", busiBidderInfo.getIsWin());
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getDelFlag()), "del_flag", busiBidderInfo.getDelFlag());
        String beginCreateTime = busiBidderInfo.getParams().get("beginCreateTime") != null ? busiBidderInfo.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = busiBidderInfo.getParams().get("endCreateTime") + "" != null ? busiBidderInfo.getParams().get("endCreateTime") + "" : "";
        busiBidderInfoQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getCreateBy()), "create_by", busiBidderInfo.getCreateBy());
        String beginUpdateTime = busiBidderInfo.getParams().get("beginUpdateTime") != null ? busiBidderInfo.getParams().get("beginUpdateTime") + "" : "";
        String endUpdateTime = busiBidderInfo.getParams().get("endUpdateTime") + "" != null ? busiBidderInfo.getParams().get("endUpdateTime") + "" : "";
        busiBidderInfoQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime, endUpdateTime);
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getUpdateBy()), "update_by", busiBidderInfo.getUpdateBy());

        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getIsAbandonedBid()), "is_abandoned_bid", busiBidderInfo.getIsAbandonedBid());
        busiBidderInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidderInfo.getAbandonedBidReason()), "abandoned_bid_reason", busiBidderInfo.getAbandonedBidReason());

        // 只显示已签到的供应商（signInStatus = 1）
        busiBidderInfoQueryWrapper.eq("sign_in_status", 1);

        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(busiBidderInfo.getProjectId());
        // 竞争性判断0，竞争性磋商1 询价3 单一来源4
        if (!tenderProject.getProjectType().equals("1")){
            busiBidderInfoQueryWrapper.orderByDesc("score", "create_time");

        }else {
            busiBidderInfoQueryWrapper.orderByAsc("bidder_amount");
        }


        return list(busiBidderInfoQueryWrapper);
    }

    @Transactional
    @Override
    public AjaxResult updateBidderInfo(BusiBidderInfo busiBidderInfo, LoginUser loginUser) throws Exception {
                BusiBiddingRecord busiBiddingRecord=new BusiBiddingRecord();
                busiBiddingRecord.setBiddingId(busiBidderInfo.getBiddingId());
                busiBiddingRecord.setSupplierKey(busiBidderInfo.getSupplierKey());
                busiBiddingRecord.setProjectId(busiBidderInfo.getProjectId());
            return     busiBiddingRecordService.responseFileDecryption(busiBiddingRecord,loginUser);

    }

    @Override
    public AjaxResult bidderAnnouncement(Long projectId) {
        List<BusiBiddingRecord> biddingRecordList = busiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>()
                .eq("project_id", projectId).eq("del_flag", 0));

        // 过滤出已签到的供应商记录
        List<BusiBiddingRecord> signedInRecords = new ArrayList<>();

        for (BusiBiddingRecord busiBiddingRecord : biddingRecordList) {
            BusiBidderInfo one = busiBidderInfoService.getOne(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId)
                    .eq("bidder_id", busiBiddingRecord.getBidderId()));
            if (null != one) {
                busiBiddingRecord.setBusiBidderInfo(one);
                busiBiddingRecord.setSignInStatus(one.getSignInStatus());

                // 只添加已签到的供应商（signInStatus = 1）
                if (one.getSignInStatus() != null && one.getSignInStatus() == 1) {
                    List<BusiAttachment> byBusiId = iBusiAttachmentService.getByBusiId(busiBiddingRecord.getBiddingId());
                    if (!byBusiId.isEmpty()) {
                        busiBiddingRecord.setAttachments(byBusiId);
                    }
                    signedInRecords.add(busiBiddingRecord);
                }
            }
            // 如果 one 为 null，说明没有对应的投标人信息，不添加到结果中
        }
        return AjaxResult.success(signedInRecords);
    }

    @Override
    public AjaxResult getBidderInfoByProjectID(Long projectId) {
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>()
                .eq("project_id", projectId)
                .eq("sign_in_status", 1)); // 只显示已签到的供应商
        busiBidderInfos.sort(Comparator.comparingInt(BusiBidderInfo::getRanking));
        return AjaxResult.success(busiBidderInfos);
    }

    @Override  //, HttpServletResponse response去掉流文件下载
    public void exportBidOpeningRecords(Long projectId) throws IOException {
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
        //获取采购人或采购单位签章
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);

        Map<String, Object> dataMap = new HashMap<>();
        TableRenderData tbbjdfTable = Tables.ofA4ExtendWidth().center().create();
        com.deepoove.poi.data.style.TableStyle tableStyle = new com.deepoove.poi.data.style.TableStyle();
        //tableStyle.setWidth();
        tableStyle.setWidth("100%");
        // 定义列数
        int columnCount = 6;
        // 定义列宽比例，例如：第一列占比10%，其他列占比剩余的90%并平均分配
        int firstColumnWidthRatio = 20;
        int otherColumnsWidthRatio = (100 - firstColumnWidthRatio) / (columnCount - 1);
        // 计算每列的宽度比例
        int[] columnWidths = new int[columnCount];
        columnWidths[0] = firstColumnWidthRatio; // 第一列宽度
        for (int i = 1; i < columnCount; i++) {
            columnWidths[i] = otherColumnsWidthRatio; // 其他列宽度
        }
        // 设置表格的列宽
        tableStyle.setColWidths(columnWidths);
        tableStyle.setTopBorder(com.deepoove.poi.data.style.BorderStyle.DEFAULT);
        tableStyle.setBottomBorder(com.deepoove.poi.data.style.BorderStyle.DEFAULT);
        tableStyle.setLeftBorder(com.deepoove.poi.data.style.BorderStyle.DEFAULT);
        tableStyle.setRightBorder(com.deepoove.poi.data.style.BorderStyle.DEFAULT);
        tableStyle.setInsideHBorder(com.deepoove.poi.data.style.BorderStyle.DEFAULT);
        tableStyle.setInsideVBorder(com.deepoove.poi.data.style.BorderStyle.DEFAULT);
        tbbjdfTable.setTableStyle(tableStyle);

        RowRenderData tbbjdfHeader = Rows.of("序号", "投标单位名称", "投标报价（元）", "签到时间", "联系人", "联系方式").center().create();
        tbbjdfTable.addRow(tbbjdfHeader);

        for (int i = 0; i < busiBidderInfos.size(); i++) {
            BusiBidderInfo bidderInfo = busiBidderInfos.get(i);
            BigDecimal bd = new BigDecimal(0.00);
            try {
                DocResponseEntInfo entInfo = docResponseEntInfoService.selectByProjectAndBidder(bidderInfo.getProjectId(), bidderInfo.getBidderId());
                if (entInfo != null && entInfo.getDetailMap() != null && entInfo.getDetailMap().get("kbylb") != null && !entInfo.getDetailMap().get("kbylb").isEmpty()) {
                    DocResponseEntDetail kbylb = entInfo.getDetailMap().get("kbylb").get(0);
                    if (StringUtils.isNoneBlank(kbylb.getDetailContent())) {
                        bd = JSONObject.parse(kbylb.getDetailContent()).getBigDecimal("bidPrice");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 创建行数据
            RowRenderData row = Rows.of(
                    String.valueOf(i + 1), // 序号
                    bidderInfo.getBidderName(), // 投标单位名称
                    bd.toString(), // 投标报价（元）
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(bidderInfo.getSignInTime()), // 签到时间
                    bidderInfo.getBidContactPerson(), // 联系人
                    bidderInfo.getBidContactPersonTel() // 联系方式
            ).center().create();

            // 将行数据添加到表格中
            tbbjdfTable.addRow(row);
        }
        dataMap.put("tbbjdf", tbbjdfTable);
        String templatePath = RuoYiConfig.getProfile() + "/templates/开标记录表.docx";
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + projectId;
        String originPath = s1 + "/开标记录表.docx";
        String genPath = s1 + "/开标记录表.pdf";
        FileUploadUtils.checkDirExists(s1);
        try {
            XWPFTemplate.compile(templatePath)
                    .render(dataMap)
                    .writeToFile(originPath);
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);
//            // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }

            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + genPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
            document.save(genPath, saveOptions);

            //关键字签章
            SignRequest signRequest = new SignRequest();
            signRequest.setSignType(2);
            signRequest.setEntKeyword("盖章");
            signRequest.setFilePath(genPath);
            Long singId = 1l;
            // 检查代理机构ID是否为空，如果不为空则使用代理机构的ID
            if (tenderProject.getAgencyId() != null) {
                singId = tenderProject.getAgencyId();
            } else {
                // 如果代理机构ID为空，则使用采购人的ID
                singId = tenderProject.getTendererId();
            }
            BaseEntInfo entInfo = iBaseEntInfoService.getById(singId);
            String base64Image = Base64.encode(entInfo.getEnterpriseSignature());
            signRequest.setEntSeal(base64Image);
            signRequest.setEntId(entInfo.getEntId());
            signRequest.setEntName(entInfo.getEntName());
            signRequest.setType(2);
            String str = qianZhang(signRequest, entInfo);
            //直接下载返回流文件
            //retrunFileOutputStream(str,response);
            BusiBidOpening busiBidOpening = iBusiBidOpeningService.getOne(new QueryWrapper<BusiBidOpening>()
                    .eq("project_id", projectId)
            );
            iBusiAttachmentService.deleteByBusiId(busiBidOpening.getBidOpeningId());
            BusiAttachment busiAttachment = new BusiAttachment();
            busiAttachment.setBusiId(busiBidOpening.getBidOpeningId());
            busiAttachment.setFilePath(AttachmentUtil.realToUrl(str));
            busiAttachment.setFileName(FileUtils.getName(str));
            String suffix = AttachmentUtil.getAttachmentSuffix(str);
            busiAttachment.setFileType("1");
            busiAttachment.setFileSuffix(suffix);
            iBusiAttachmentService.save(busiAttachment);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void exportBidOpeningRecords1(Long projectId, HttpServletResponse response) throws IOException {
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
        //获取采购人或采购单位签章
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        Map<String, Object> dataMap = new HashMap<>();
        TableRenderData tbbjdfTable = Tables.ofA4ExtendWidth().center().create();
        com.deepoove.poi.data.style.TableStyle tableStyle = new com.deepoove.poi.data.style.TableStyle();
        tableStyle.setWidth("100%");
        // 定义列数
        int columnCount = 6;
        // 定义列宽比例，例如：第一列占比10%，其他列占比剩余的90%并平均分配
        int firstColumnWidthRatio = 10;
        int otherColumnsWidthRatio = (100 - firstColumnWidthRatio) / (columnCount - 1);
        // 计算每列的宽度比例
        int[] columnWidths = new int[columnCount];
        columnWidths[0] = firstColumnWidthRatio; // 第一列宽度
        for (int i = 1; i < columnCount; i++) {
            columnWidths[i] = otherColumnsWidthRatio; // 其他列宽度
        }
        RowStyle rowStyle = new RowStyle();
        // 设置行高，例如设置为1厘米（1440 EMU）
        int heightInEMU = 1440; // 1厘米等于1440 EMU
        rowStyle.setHeight(heightInEMU);
        com.deepoove.poi.data.style.CellStyle cellStyle = new com.deepoove.poi.data.style.CellStyle();
        // 设置文字垂直居中对齐
        cellStyle.setVertAlign(XWPFTableCell.XWPFVertAlign.CENTER);
// 创建ParagraphStyle的Builder
        ParagraphStyle.Builder paragraphStyleBuilder = ParagraphStyle.builder();
// 设置段落水平居中对齐
        paragraphStyleBuilder.withAlign(ParagraphAlignment.CENTER);
// 构建ParagraphStyle实例
        ParagraphStyle paragraphStyle = paragraphStyleBuilder.build();
        cellStyle.setDefaultParagraphStyle(paragraphStyle);
        rowStyle.setDefaultCellStyle(cellStyle);
// 设置行高规则，通常使用"exact"表示精确值
        rowStyle.setHeightRule("exact");
        // 设置表格的列宽
        tableStyle.setColWidths(columnWidths);
        tbbjdfTable.setTableStyle(tableStyle);
        RowRenderData tbbjdfHeader = Rows.of("序号", "投标单位名称", "投标报价（元）", "签到时间", "联系人", "联系方式").center().create();
        tbbjdfHeader.setRowStyle(rowStyle);
        tbbjdfTable.addRow(tbbjdfHeader);

        for (int i = 0; i < busiBidderInfos.size(); i++) {
            BusiBidderInfo bidderInfo = busiBidderInfos.get(i);
            BigDecimal bd = new BigDecimal(0.00);
            try {
                DocResponseEntInfo entInfo = docResponseEntInfoService.selectByProjectAndBidder(bidderInfo.getProjectId(), bidderInfo.getBidderId());
                if (entInfo != null && entInfo.getDetailMap() != null && entInfo.getDetailMap().get("kbylb") != null && !entInfo.getDetailMap().get("kbylb").isEmpty()) {
                    DocResponseEntDetail kbylb = entInfo.getDetailMap().get("kbylb").get(0);
                    if (StringUtils.isNoneBlank(kbylb.getDetailContent())) {
                        bd = JSONObject.parse(kbylb.getDetailContent()).getBigDecimal("bidPrice");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 创建行数据
            RowRenderData row = Rows.of(
                    String.valueOf(i + 1), // 序号
                    bidderInfo.getBidderName(), // 投标单位名称
                    bd.toString(), // 投标报价（元）
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(bidderInfo.getSignInTime()), // 签到时间
                    bidderInfo.getBidContactPerson(), // 联系人
                    bidderInfo.getBidContactPersonTel() // 联系方式
            ).center().create();
            row.setRowStyle(rowStyle);
            // 将行数据添加到表格中
            tbbjdfTable.addRow(row);
        }
        dataMap.put("tbbjdf", tbbjdfTable);
        String templatePath = RuoYiConfig.getProfile() + "/templates/开标记录表.docx";
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + projectId;
        String originPath = s1 + "/开标记录表.docx";
        String genPath = s1 + "/开标记录表.pdf";
        FileUploadUtils.checkDirExists(s1);
        try {
            XWPFTemplate.compile(templatePath)
                    .render(dataMap)
                    .writeToFile(originPath);
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);
//            // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }

            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + genPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
            document.save(genPath, saveOptions);

            //关键字签章
            SignRequest signRequest = new SignRequest();
            signRequest.setSignType(2);
            signRequest.setEntKeyword("盖章");
            signRequest.setFilePath(genPath);
            Long singId = 1l;
            // 检查代理机构ID是否为空，如果不为空则使用代理机构的ID
            if (tenderProject.getAgencyId() != null) {
                singId = tenderProject.getAgencyId();
            } else {
                // 如果代理机构ID为空，则使用采购人的ID
                singId = tenderProject.getTendererId();
            }
            BaseEntInfo entInfo = iBaseEntInfoService.getById(singId);
            String base64Image = Base64.encode(entInfo.getEnterpriseSignature());
            signRequest.setEntSeal(base64Image);
            signRequest.setEntId(entInfo.getEntId());
            signRequest.setEntName(entInfo.getEntName());
            signRequest.setType(2);
            String str = qianZhang(signRequest, entInfo);
            //直接下载返回流文件
            retrunFileOutputStream(str, response);
          /*  BusiBidOpening busiBidOpening = iBusiBidOpeningService.getOne(new QueryWrapper<BusiBidOpening>()
                    .eq("project_id", projectId)
            );
            BusiAttachment busiAttachment=new BusiAttachment();
            busiAttachment.setBusiId(busiBidOpening.getBidOpeningId());
            busiAttachment.setFilePath(str);
            busiAttachment.setFileName(FileUtils.getName(str));
            String suffix = busiAttachment.getFileName().substring(busiAttachment.getFileName().lastIndexOf("."));
            busiAttachment.setFileType("1");
            busiAttachment.setFileSuffix(suffix);
            iBusiAttachmentService.save(busiAttachment);*/
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public void retrunFileOutputStream(String str, HttpServletResponse response) {
        // 确保str是有效的文件路径
        File pdfFile = new File(str);

        // 设置响应头信息
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + pdfFile.getName() + "\"");
        response.setContentLengthLong(pdfFile.length());

        // 缓冲区大小常量
        final int BUFFER_SIZE = 4096;

        try (
                // 创建一个缓冲输入流来读取PDF文件
                BufferedInputStream bufferedInputStream = new BufferedInputStream(new FileInputStream(pdfFile));
                // 获取响应的输出流
                OutputStream responseOutputStream = response.getOutputStream()
        ) {
            // 创建一个缓冲区
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;

            // 将PDF文件内容写入到响应输出流
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                responseOutputStream.write(buffer, 0, bytesRead);
            }
            // 注意：不需要显式调用flush()和close()，因为try-with-resources会自动处理
        } catch (FileNotFoundException e) {
            // 处理文件未找到异常
            throw new RuntimeException("PDF file not found", e);
        } catch (IOException e) {
            // 处理其他I/O异常
            throw new RuntimeException("Error while writing PDF to output stream", e);
        }
    }

    public String qianZhang(SignRequest request, BaseEntInfo entInfo) {
        byte[] signFileBytes = null;
        byte[] entSealBytes = null;
        byte[] legalPersonalSealBytes = null;
        Result<CertEventResponse> entCert = null;
        Result<CertEventResponse> legalPersonalCert = null;
        List<RealPositionProperty> entPositionList = null;
        List<RealPositionProperty> personalPositionList = null;
        List<RealPositionProperty> legalPersonalPositionList = null;
        int entSealWidth = 200;
        int entSealHeight = 200;
        int personalSealWidth = 150;
        int personalSealHeight = 70;
        int legalPersonalSealWidth = 100;
        int legalPersonalSealHeight = 100;
        //获取本地签署文件
        try {
            signFileBytes = getResourceFiles(request.getFilePath());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (signFileBytes == null) {
            throw new RuntimeException("签署失败，未获取到响应文件");
        }
        //生成企业证书和个人证书
        try {
            if (request.getEntName() != null && request.getEntName().length() > 0) {
                CertEventRequest certEventRequest = new CertEventRequest();
                certEventRequest.setCertSubject("政府采购限额以下交易平台@" + request.getEntName());
                certEventRequest.setCertPassword("123456");
                certEventRequest.setUniqueCode(UUID.randomUUID().toString());
                entCert = sdkService.certEvent(certEventRequest);
                if (entCert == null || !entCert.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
                    throw new RuntimeException(entCert.getMessage());
                }
            }
            String legalPersonalName = "";
            if (com.ruoyi.common.utils.StringUtils.isNotBlank(entInfo.getEntLegalPerson())) {
                legalPersonalName = entInfo.getEntLegalPerson();
            } else {
                legalPersonalName = entInfo.getEntName();
            }
            if (com.ruoyi.common.utils.StringUtils.isNotBlank(legalPersonalName)) {
                CertEventRequest certEventRequest = new CertEventRequest();
                certEventRequest.setCertSubject("政府采购限额以下交易平台@" + legalPersonalName);
                certEventRequest.setCertPassword("123456");
                certEventRequest.setUniqueCode(UUID.randomUUID().toString());
                legalPersonalCert = sdkService.certEvent(certEventRequest);
                if (legalPersonalCert == null || !legalPersonalCert.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
                    throw new RuntimeException(legalPersonalCert.getMessage());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        //生成企业签章和个人签章
        if (request.getEntSeal() != null) {
            entSealBytes = decode(request.getEntSeal());
        }
        if (request.getLegalPersonalSeal() != null) {
            legalPersonalSealBytes = decode(request.getLegalPersonalSeal());
        }

        //进行签署操作
        byte[] operationByte = signFileBytes;

        //计算企业签署位置和个人签署位置
        if (SignTypeEnum.POSITION.getCode().equals(request.getSignType())) {
            DocumentSignRequest signRequest = new DocumentSignRequest();
            signRequest.setUniqueCode(UUID.randomUUID().toString());
            signRequest.setSignType(SDKSignTypeEnum.POSITION.getCode());
            log.debug("--------------------sign-------------------------");
            if ((request.getEntPositionList() == null || request.getEntPositionList().size() == 0) &&
                    (request.getLegalPersonPositionList() == null || request.getLegalPersonPositionList().size() == 0)) {
                throw new RuntimeException("签署失败，未获取到签章位置信息");

            }
            //计算企业签署位置
            if (request.getEntPositionList() != null && request.getEntPositionList().size() > 0) {
                signRequest.setCertPassword(entCert.getData().getCertPassword());
                signRequest.setPfx(entCert.getData().getPfx());
                signRequest.setSignatureFile(encode(entSealBytes));
                signRequest.setDocumentFile(encode(operationByte));

                List<SignLocation> signLocations = new ArrayList<>();
                for (PositionRequest positionRequest : request.getEntPositionList()) {
                    SignLocation signLocation = new SignLocation();
                    signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                    signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                    signLocation.setPage(positionRequest.getPage());
                    signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                    signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                    signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                    signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                    signLocations.add(signLocation);
                }
                signRequest.setSignLocationList(signLocations);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if (signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();
                } else {
                    throw new RuntimeException("签署失败，单页签署企业签章失败");
                }

            }
            //计算个人签署位置
            if (request.getLegalPersonPositionList() != null && request.getLegalPersonPositionList().size() > 0) {

                signRequest.setCertPassword(legalPersonalCert.getData().getCertPassword());
                signRequest.setPfx(legalPersonalCert.getData().getPfx());
                signRequest.setSignatureFile(encode(legalPersonalSealBytes));
                signRequest.setDocumentFile(encode(operationByte));

                List<SignLocation> signLocations = new ArrayList<>();
                for (PositionRequest positionRequest : request.getLegalPersonPositionList()) {
                    SignLocation signLocation = new SignLocation();
                    signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                    signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                    signLocation.setPage(positionRequest.getPage());
                    signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                    signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                    signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                    signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                    signLocations.add(signLocation);
                }
                signRequest.setSignLocationList(signLocations);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if (signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();

                } else {
                    throw new RuntimeException("签署失败，单页签署个人签章失败");
                }
            }

        } else if (SignTypeEnum.KEYWORDS.getCode().equals(request.getSignType())) {
            if (com.ruoyi.common.utils.StringUtils.isBlank(request.getEntKeyword()) && com.ruoyi.common.utils.StringUtils.isBlank(request.getLegalKeyword())) {
                throw new RuntimeException("签署失败");
            }
            DocumentSignRequest signRequest = new DocumentSignRequest();
            signRequest.setUniqueCode(UUID.randomUUID().toString());
            signRequest.setSignType(SDKSignTypeEnum.KEYWORDS.getCode());
            //根据关键字计算所有企业签署位置
            if (request.getEntKeyword() != null && request.getEntKeyword().length() > 0) {
                entPositionList = calculatePositionService.getAllPositionByKeyWords(signFileBytes, request.getEntKeyword(), entSealWidth, entSealHeight, 1);
                signRequest.setCertPassword(entCert.getData().getCertPassword());
                signRequest.setPfx(entCert.getData().getPfx());
                signRequest.setSignatureFile(encode(entSealBytes));
                signRequest.setDocumentFile(encode(operationByte));
                signRequest.setKeywords(request.getEntKeyword());
                signRequest.setKeywordType(1);
                System.out.println("企业关键字签署signRequest:" + JSONObject.toJSONString(signRequest));
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                System.out.println("企业关键字签署signResponse:" + JSONObject.toJSONString(signResponse));
                if (signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();
                } else {
                    //  return AjaxResult.error("");
                    throw new RuntimeException("企业关键字签署失败");

                }
            }
            //根据关键字计算所有个人签署位置
            if (request.getLegalKeyword() != null && request.getLegalKeyword().length() > 0) {
                personalPositionList = calculatePositionService.getAllPositionByKeyWords(signFileBytes, request.getLegalKeyword(), legalPersonalSealWidth, legalPersonalSealHeight, 2);
                signRequest.setCertPassword(legalPersonalCert.getData().getCertPassword());
                signRequest.setPfx(legalPersonalCert.getData().getPfx());
                signRequest.setSignatureFile(encode(legalPersonalSealBytes));
                signRequest.setDocumentFile(encode(operationByte));
                signRequest.setKeywords(request.getLegalKeyword());
                signRequest.setKeywordType(2);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if (signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();
                } else {
                    throw new RuntimeException("法定代表人关键字签署失败");
                }
            }

            if ((personalPositionList == null || personalPositionList.size() == 0) &&
                    (entPositionList == null || entPositionList.size() == 0)) {
                //return AjaxResult.error("签署失败！签署关键字在文件中不存在，请准确设置关键字后再签署");
                throw new RuntimeException("签署失败！签署关键字在文件中不存在，请准确设置关键字后再签署");
            }
        } else if (SignTypeEnum.ALL.getCode().equals(request.getSignType())) {
            DocumentSignRequest signRequest = new DocumentSignRequest();
            signRequest.setUniqueCode(UUID.randomUUID().toString());
            signRequest.setSignType(SDKSignTypeEnum.POSITION.getCode());
            if ((request.getEntPositionList() == null || request.getEntPositionList().size() == 0) &&
                    (request.getPersonalPositionList() == null || request.getPersonalPositionList().size() == 0)) {
                throw new RuntimeException("签署失败");

            }
            //计算企业签署位置
            if (request.getEntPositionList() != null && request.getEntPositionList().size() > 0) {
                signRequest.setCertPassword(entCert.getData().getCertPassword());
                signRequest.setPfx(entCert.getData().getPfx());
                signRequest.setSignatureFile(encode(entSealBytes));
                signRequest.setDocumentFile(encode(operationByte));

                List<SignLocation> signLocations = new ArrayList<>();
                for (PositionRequest positionRequest : request.getEntPositionList()) {
                    for (int i = 1; i <= request.getPageSize(); i++) {
                        SignLocation signLocation = new SignLocation();
                        signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                        signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                        signLocation.setPage(i);
                        signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                        signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                        signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                        signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                        signLocations.add(signLocation);
                    }
                }
                signRequest.setSignLocationList(signLocations);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if (signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();
                } else {
                    throw new RuntimeException("签署失败");

                }

            }
            //计算个人签署位置
            if (request.getLegalPersonPositionList() != null && request.getLegalPersonPositionList().size() > 0) {

                signRequest.setCertPassword(entCert.getData().getCertPassword());
                signRequest.setPfx(entCert.getData().getPfx());
                signRequest.setSignatureFile(encode(legalPersonalSealBytes));
                signRequest.setDocumentFile(encode(operationByte));

                List<SignLocation> signLocations = new ArrayList<>();
                for (PositionRequest positionRequest : request.getLegalPersonPositionList()) {
                    SignLocation signLocation = new SignLocation();
                    signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                    signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                    signLocation.setPage(positionRequest.getPage());
                    signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                    signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                    signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                    signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                    signLocations.add(signLocation);
                }
                signRequest.setSignLocationList(signLocations);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if (signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();

                } else {
                    throw new RuntimeException("签署失败");
                }
            }

        }

        Path path = Paths.get(request.getFilePath());
        // 获取目录路径
        Path directoryPath = path.getParent();
        // 生成UUID作为文件名
        String uuidFileName = UUID.randomUUID().toString() + ".pdf";
        // 创建新的文件路径
        Path newFilePath = directoryPath.resolve(uuidFileName);
        System.out.println(newFilePath.toString());
        try (ByteArrayInputStream is = new ByteArrayInputStream(operationByte);
             PdfReader reader = new PdfReader(is);
             ByteArrayOutputStream os = new ByteArrayOutputStream();
             PdfWriter writer = new PdfWriter(os);
             PdfDocument pdfDocument = new PdfDocument(reader, writer);
             FileOutputStream fos = new FileOutputStream(newFilePath.toString())) {
            // FileOutputStream fos = new FileOutputStream("D:\\ruoyi\\uploadPath\\222.pdf")) {
            // 这里可以添加对PDF文档的操作，例如添加内容、修改属性等
            // 关闭文档
            pdfDocument.close();
            // 将 ByteArrayOutputStream 中的内容写入文件
            os.writeTo(fos);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return newFilePath.toString();
    }

    public byte[] getResourceFiles(String path) {
        try {
            //  InputStream inputStream = ResourceUtils.class.getClassLoader()
            //   .getResourceAsStream(path);
            //String realPath = AttachmentUtil.urlToReal(path);
            InputStream inputStream = Files.newInputStream(Paths.get(path));
            return read(inputStream);
        } catch (Exception e) {
            System.err.println(path);
            e.printStackTrace();
        }
        return null;
    }

    public byte[] read(InputStream inputStream) throws IOException {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int num = inputStream.read(buffer);
            while (num != -1) {
                baos.write(buffer, 0, num);
                num = inputStream.read(buffer);
            }
            baos.flush();
            return baos.toByteArray();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    @Override
    public AjaxResult signIn(LoginUser loginUser, BusiBidderInfo busiBidderInfo) {

        busiBidderInfo.setBidderId(loginUser.getEntId());
        busiBidderInfo.setBidderName(loginUser.getUser().getEnt().getEntName());
        busiBidderInfo.setBidderCode(loginUser.getUser().getEnt().getEntCode());
        busiBidderInfo.setDecodeFlag(0);//解密标识签到时是0
        busiBidderInfo.setSignInStatus(1);
        busiBidderInfo.setSignInTime(new Date());
        List<BusiBidderInfo> list = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>()
                .eq("bidder_id", loginUser.getEntId())
                .eq("project_id", busiBidderInfo.getProjectId())
        );
        if (list.isEmpty()) {
            // list为空，在这里处理空列表的情况
            busiBidderInfoService.save(busiBidderInfo);
            return AjaxResult.success(busiBidderInfo);
        } else {
            return AjaxResult.error("供应商已签到");
        }
    }

    private void setColumnWidths(Sheet sheet) {
        int[] widths = {10, 20, 15, 20, 15, 20};
        for (int i = 0; i < widths.length; i++) {
            sheet.setColumnWidth(i, 256 * widths[i]);
        }
    }

    private CellStyle createTitleCellStyle(Workbook workbook, short TITLE_FONT_SIZE) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setFontHeightInPoints(TITLE_FONT_SIZE);
        font.setBold(true);
        style.setFont(font);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        return style;
    }

    private CellStyle createDataCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        return style;
    }

    private void setHeaderRow(String[] headers, Row row, CellStyle dataStyle) {
        for (int i = 0; i < headers.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(dataStyle);
        }
    }

    private void fillRow(Row row, CellStyle dataStyle, int index, BusiBidderInfo info) {
        BigDecimal bd = new BigDecimal(0.00);
        try {
            DocResponseEntInfo entInfo = docResponseEntInfoService.selectByProjectAndBidder(info.getProjectId(), info.getBidderId());
            if (entInfo != null && entInfo.getDetailMap() != null && entInfo.getDetailMap().get("kbylb") != null && !entInfo.getDetailMap().get("kbylb").isEmpty()) {
                DocResponseEntDetail kbylb = entInfo.getDetailMap().get("kbylb").get(0);
                if (StringUtils.isNoneBlank(kbylb.getDetailContent())) {
                    bd = JSONObject.parse(kbylb.getDetailContent()).getBigDecimal("bidPrice");
                    BusiBiddingRecord b = busiBiddingRecordService.selectByProjectAndBidder(info.getProjectId(), info.getBidderId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        String[] headers = {"序号", "投标单位名称", "投标报价（元）", "签到时间", "联系人", "联系方式"};
        for (int j = 0; j < headers.length; j++) {
            Cell cell = row.createCell(j);
            switch (j) {
                case 0:
                    cell.setCellValue(index);
                    break;
                case 1:
                    row.createCell(1).setCellValue(info.getBidderName());
                    break;
                case 2:
                    row.createCell(2).setCellValue(bd.toString());
                    break;
                case 3:
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    row.createCell(3).setCellValue(dateFormat.format(info.getSignInTime()));
                    break;
                case 4:
                    row.createCell(4).setCellValue(info.getBidContactPerson());
                    break;
                case 5:
                    row.createCell(5).setCellValue(info.getBidContactPersonTel());
                    break;
                default:
                    break;
            }
            cell.setCellStyle(dataStyle);
        }
    }
}
